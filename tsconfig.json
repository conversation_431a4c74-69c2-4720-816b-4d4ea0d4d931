{
  "compilerOptions": {
    "target": "ES2016",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "paths": {
      "@components": ["src/components/index"],
      // "@constants": ["src/constants/"],
      "@navigators": ["src/Navigation/index"],
      "@screens": ["src/Screen/index"],
      "@utils": ["src/utils/index"],
      "@css/*":["src/css/*"],

    }
  },
  "include": ["src"],
  
  
}
