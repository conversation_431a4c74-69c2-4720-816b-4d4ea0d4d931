{"name": "DeltaPOS", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^7.8.0", "@react-native-async-storage/async-storage": "^1.17.12", "@react-native-community/checkbox": "^0.5.16", "@react-native-community/netinfo": "^9.3.7", "@react-native/metro-config": "^0.72.11", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@reduxjs/toolkit": "^1.9.3", "axios": "^1.3.4", "fast-xml-parser": "3.12.13", "formik": "^2.2.9", "lodash": "^4.17.21", "metro-config": "^0.79.0", "moment": "^2.29.4", "picker": "^0.1.4", "react": "18.2.0", "react-csv": "^2.2.2", "react-native": "0.72.4", "react-native-blob-util": "^0.17.3", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^4.2.9", "react-native-device-info": "^11.1.0", "react-native-document-picker": "^8.2.0", "react-native-draggable-flatlist": "^4.0.2", "react-native-draggable-grid": "^2.2.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-gradle-plugin": "^0.71.16", "react-native-image-picker": "^5.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-mail": "^6.1.1", "react-native-network-info": "^5.2.1", "react-native-picker-select": "^8.0.4", "react-native-ping": "^1.2.8", "react-native-radio-buttons-group": "^3.1.0", "react-native-reanimated": "3.5.4", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^4.5.0", "react-native-screens": "^3.31.1", "react-native-select-dropdown": "^3.3.2", "react-native-svg": "12.4.3", "react-native-svg-transformer": "^1.1.0", "react-native-table-component": "^1.2.2", "react-native-tcp-socket": "^6.3.0", "react-native-vector-icons": "^9.2.0", "react-redux": "^8.0.5", "redux": "^4.2.1", "xlsx": "^0.18.5", "yup": "^1.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/lodash": "^4.14.192", "@types/react": "^18.0.24", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "fix": "^0.0.6", "jest": "^29.2.1", "metro": "^0.79.0", "metro-core": "^0.79.0", "metro-react-native-babel-preset": "0.73.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}