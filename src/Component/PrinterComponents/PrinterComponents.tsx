import React, { useState } from 'react';
import {
  SafeAreaView,
  TouchableOpacity,
  FlatList,
  TextInput,
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { ThunkDispatch } from '@reduxjs/toolkit';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { setShowListPrinter } from '../../Redux/Slide';
import { heightWindow, scale, widthWindow } from '../../utils/style/Reponsive';
import { scanLANForPrinters } from '../../utils/scanLANForPrinters';

interface PrinterForm {
  id: string;
  ip: string;
  name: string;
  type: string;
  paperSize: string;
  isDefault: boolean;
  showTypeDropdown: boolean;
  showPaperDropdown: boolean;
}

const printerTypes = ['kitchen', 'receipt'];
const paperSizes = ['58mm', '80mm'];
const generateId = () => `${Date.now()}-${Math.floor(Math.random() * 100000)}`;

const PrinterComponents = () => {
  const [forms, setForms] = useState<PrinterForm[]>([
    {
      id: generateId(),
      ip: '',
      name: '',
      type: 'Chọn loại máy in',
      paperSize: 'Chọn khổ giấy',
      isDefault: false,
      showTypeDropdown: false,
      showPaperDropdown: false,
    },
  ]);

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const xmlData = useSelector((state: any) => state.getData.xmlData?.screen);
  const { maincolor, blackcolor } = xmlData || {};

  const close = () => {
    dispatch(setShowListPrinter(false));
  };

  const toggleCheckbox = (id: string) => {
    // Chỉ cho phép một máy in mặc định
    setForms(forms.map(form => ({
      ...form,
      isDefault: form.id === id
    })));
  };

  const scanNet = async () => {
    try {
      setLoading(true);
      const printers = await scanLANForPrinters();
      console.log('Máy in tìm thấy:', printers);
    } catch (error) {
      console.error('Lỗi khi quét mạng:', error);
    } finally {
      setLoading(false);
    }
  };

  const addPrinterForm = () => {
    const newForm: PrinterForm = {
      id: generateId(),
      ip: '',
      name: '',
      type: 'Chọn loại máy in',
      paperSize: 'Chọn khổ giấy',
      isDefault: false,
      showTypeDropdown: false,
      showPaperDropdown: false,
    };
    setForms(prev => [...prev, newForm]);
  };

  const updateFormField = (id: string, field: keyof PrinterForm, value: string | boolean) => {
    setForms(prev =>
      prev.map(form => (form.id === id ? { ...form, [field]: value } : form))
    );
  };

  const renderDropdown = (
    id: string,
    selectedValue: string,
    fieldName: keyof PrinterForm,
    items: string[],
    placeholder: string,
    showDropdown: boolean,
    showFieldName: keyof PrinterForm
  ) => (
    <View style={styles.inputContainer}>
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => updateFormField(id, showFieldName, !showDropdown)}
      >
        <Text style={[styles.dropdownText, selectedValue === placeholder && { color: '#999' }]}>
          {selectedValue}
        </Text>
      </TouchableOpacity>
      {showDropdown && (
        <View style={styles.dropdownContainer}>
          <FlatList
            data={items}
            keyExtractor={(item) => item}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={() => {
                  updateFormField(id, fieldName, item);
                  updateFormField(id, showFieldName, false);
                }}
              >
                <Text style={styles.dropdownItemText}>{item}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
    </View>
  );

  const renderForm = ({ item }: { item: PrinterForm }) => (
    <View style={styles.formContainer}>
      <View style={styles.inputRow}>
        <Text style={styles.label}>Địa chỉ IP</Text>
        <TextInput
          value={item.ip}
          onChangeText={(text) => updateFormField(item.id, 'ip', text)}
          style={styles.textInput}
          placeholder="Nhập địa chỉ IP"
          placeholderTextColor="#999"
        />
      </View>
      <View style={styles.inputRow}>
        <Text style={styles.label}>Tên máy in</Text>
        <TextInput
          value={item.name}
          onChangeText={(text) => updateFormField(item.id, 'name', text)}
          style={styles.textInput}
          placeholder="Nhập tên máy in"
          placeholderTextColor="#999"
        />
      </View>
      <View style={styles.inputRow}>
        <Text style={styles.label}>Loại máy in</Text>
        {renderDropdown(
          item.id,
          item.type,
          'type',
          printerTypes,
          'Chọn loại máy in',
          item.showTypeDropdown,
          'showTypeDropdown'
        )}
      </View>
      <View style={styles.inputRow}>
        <Text style={styles.label}>Khổ giấy</Text>
        {renderDropdown(
          item.id,
          item.paperSize,
          'paperSize',
          paperSizes,
          'Chọn khổ giấy',
          item.showPaperDropdown,
          'showPaperDropdown'
        )}
      </View>
      <View style={styles.inputRow}>
        <Text style={styles.label}>Mặc định</Text>
        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox(item.id)}>
          <View
            style={[
              styles.checkbox,
              item.isDefault && { backgroundColor: maincolor || '#007AFF', borderColor: maincolor || '#007AFF' },
            ]}
          >
            {item.isDefault && <Text style={styles.checkboxText}>✓</Text>}
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  const savePrinters = () => {
    console.log('Danh sách máy in:', forms);
    alert('Đã lưu danh sách máy in.');
  };

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={forms}
        renderItem={renderForm}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={
          <View style={styles.header}>
            <Text style={[styles.title, { color: maincolor || '#000' }]}>Quản lý máy in</Text>
            {loading ? (
              <ActivityIndicator size="small" color={maincolor || '#007AFF'} />
            ) : (
              <Ionicons name="search" size={34} color={blackcolor || '#000'} onPress={scanNet} />
            )}
          </View>
        }
        contentContainerStyle={styles.listContent}
      />
      <View style={styles.footer}>
        <TouchableOpacity style={[styles.button, { backgroundColor: maincolor || '#0EA5E9' }]} onPress={savePrinters}>
          <Text style={styles.buttonText}>Lưu</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, { backgroundColor: maincolor || '#0EA5E9' }]} onPress={addPrinterForm}>
          <Text style={styles.buttonText}>Thêm máy in</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, { backgroundColor: 'red' }]} onPress={close}>
          <Text style={styles.buttonText}>Thoát</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    marginHorizontal: scale(5),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(10),
    marginVertical: scale(10),
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: scale(20),
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: scale(10),
    backgroundColor: '#f9f9f9',
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: scale(5),
  },
  label: {
    fontSize: 16,
    color: '#000',
  },
  textInput: {
    width: '70%',
    borderWidth: 2,
    borderColor: '#4A90E2',
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: '#FFF',
  },
  inputContainer: {
    width: '70%',
    marginVertical: 2,
  },
  dropdownButton: {
    borderWidth: 2,
    borderColor: '#4A90E2',
    borderRadius: 8,
    padding: 10,
    backgroundColor: '#FFF',
    justifyContent: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#000',
  },
  dropdownContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginTop: 4,
    maxHeight: 120,
  },
  dropdownItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#000',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#000',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  checkboxText: {
    color: '#fff',
    fontSize: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: scale(10),
  },
  button: {
    width: widthWindow * 0.3,
    height: heightWindow * 0.06,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: widthWindow * 0.04,
  },
  listContent: {
    paddingBottom: scale(20),
  },
});

export default PrinterComponents;
